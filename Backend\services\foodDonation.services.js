import { FoodDonation } from "../models/fooddonationSchema.js";

export const createFoodDonation = async ({ donorId, itemName, quantity, pickupLocation }) => {
  if (!donorId || !itemName || !quantity || !pickupLocation) {
    throw new Error("Missing required fields");
  }

  const foodDonation = await FoodDonation.create({
    donorId,
    itemName,
    quantity,
    pickupLocation,
  });

  return await foodDonation.populate("donorId", "name email");
};

export const getAllFoodDonations = async () => {
  return await FoodDonation.find({})
    .populate("donorId", "name email")
    .populate("claimedBy", "name email");
};

export const getFoodDonationById = async (id) => {
  if (!id) {
    throw new Error("Missing required fields");
  }

  return await FoodDonation.findById(id)
    .populate("donorId", "name email")
    .populate("claimedBy", "name email");
};

export const claimFoodDonation = async ({ foodDonationId, claimedBy }) => {
  if (!foodDonationId || !claimedBy) {
    throw new Error("Missing required fields");
  }

  const donation = await FoodDonation.findById(foodDonationId);
  if (!donation) {
    throw new Error("Food donation not found");
  }

  // ⛔ Prevent donor from claiming their own food
  if (donation.donorId.toString() === claimedBy.toString()) {
    throw new Error("Donor cannot claim their own donation");
  }

  if (donation.status !== "pending") {
    throw new Error("Food donation already claimed or cancelled");
  }

  if (donation.claimedBy) {
    throw new Error("Food donation already claimed");
  }

  const updatedDonation = await FoodDonation.findByIdAndUpdate(
    foodDonationId,
    {
      claimedBy,
      claimedAt: Date.now(),
      status: "collected",
    },
    { new: true }
  )
    .populate("donorId", "name email")
    .populate("claimedBy", "name email");

  return updatedDonation;
};

export const cancelFoodDonation = async (foodDonationId) => {
  if (!foodDonationId) {
    throw new Error("Missing required fields");
  }

  const donation = await FoodDonation.findById(foodDonationId);
  if (!donation) {
    throw new Error("Food donation not found");
  }

  return await FoodDonation.findByIdAndUpdate(
    foodDonationId,
    { status: "cancelled" },
    { new: true }
  )
    .populate("donorId", "name email")
    .populate("claimedBy", "name email");
};
