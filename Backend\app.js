import dotenv from 'dotenv';
dotenv.config();
import express, { urlencoded } from 'express';
import connect from './db/db.js';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import userRoutes from './routes/user.routes.js';
import { authMiddleware } from './middlewares/auth.middleware.js';
import serviceRoutes from './routes/services.routes.js';
import applicationRoutes from './routes/application.routes.js';
import jobRoutes from './routes/job.routes.js';
import notificationRoutes from './routes/notification.routes.js';
import foodDonationRoutes from './routes/foodDonation.routes.js';
connect();


const app=express();
app.use(cors({
    origin: ["http://localhost:5173", "http://localhost:5174", "http://localhost:5175"],
    credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({extended:true}));
app.use(cookieParser());

app.get('/', (_req, res) => {
    res.send('Hello From home');
});

app.use('/user', userRoutes);
app.use('/service', serviceRoutes);
app.use('/application', applicationRoutes);
app.use('/job', jobRoutes);
app.use('/notification', notificationRoutes);
app.use('/foodDonation', foodDonationRoutes);


export default app;



