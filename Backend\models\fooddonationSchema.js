import mongoose from "mongoose";

const foodDonationSchema = new mongoose.Schema({

    donorId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true
    },
    itemName: {
        type: String,
        required: true
    },
    quantity: {
        type: Number,
        required: true
    },
    pickupLocation: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ["pending", "collected", "cancelled"],
        default: "pending"
    },
    claimedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        default: null
    },
    claimedAt: {
        type: Date,
        default: null
    }

}, { timestamps: true });

export const FoodDonation = mongoose.model("FoodDonation", foodDonationSchema);