import * as foodDonationServices from '../services/foodDonation.services.js';
import { validationResult } from 'express-validator';
import { FoodDonation } from '../models/fooddonationSchema.js';

export const createFoodDonationController = async (req, res) => {
    const error = validationResult(req);
    if(!error.isEmpty()){
        return res.status(400).json({errors: error.array()});
    }
    try{
        const foodDonation = await foodDonationServices.createFoodDonation({...req.body, donorId: req.user._id});
        res.status(201).json({
            success: true,
            message: "Food donation created successfully",
            foodDonation
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}

export const getAllFoodDonationsController = async (_req, res) => {
    try{
        const foodDonations = await foodDonationServices.getAllFoodDonations();
        res.status(200).json({
            success: true,
            message: "Food donations fetched successfully",
            foodDonations
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}
export const getFoodDonationByIdController = async (req, res) => {
    if(!req.params.id){
        return res.status(400).json({
            success: false,
            message: "Missing required fields"
        });
    }
    try{
        const foodDonation = await foodDonationServices.getFoodDonationById(req.params.id);
        if(!foodDonation){
            return res.status(404).json({
                success: false,
                message: "Food donation not found"
            });
        }
        res.status(200).json({
            success: true,
            message: "Food donation fetched successfully",
            foodDonation
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}

export const claimFoodDonationController = async (req, res) => {
    

    try{
        const foodDonation = await foodDonationServices.claimFoodDonation({
            foodDonationId: req.params.id,
            claimedBy: req.user._id
        });
        res.status(200).json({
            success: true,
            message: "Food donation claimed successfully",
            foodDonation
        });
    }
    catch(err){
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}

export const cancelFoodDonationController = async (req, res) => {
        if(!req.params.id){
            return res.status(400).json({
                success: false,
                message: "Missing required fields"
            });
        }
        try{
            const foodDonation = await foodDonationServices.cancelFoodDonation(req.params.id);
            res.status(200).json({
                success: true,
                message: "Food donation cancelled successfully",
                foodDonation
            });
        }
        catch(err){
            res.status(500).json({
                success: false,
                message: err.message
            });
        }
}