import { Router } from 'express';
import * as foodDonationController from '../controllers/foodDonation.controller.js';
import { body } from 'express-validator';
import { authMiddleware } from '../middlewares/auth.middleware.js';

const router = Router();

router.post('/create', authMiddleware, [body('itemName').notEmpty().withMessage('Item name is required'), body('quantity').isNumeric().withMessage('Quantity must be a number').isInt({min: 1}).withMessage('Quantity must be at least 1'), body('pickupLocation').notEmpty().withMessage('Pickup location is required')], foodDonationController.createFoodDonationController);
router.get('/all', foodDonationController.getAllFoodDonationsController);
router.get('/:id', foodDonationController.getFoodDonationByIdController);
router.put('/claim/:id', authMiddleware, foodDonationController.claimFoodDonationController);
router.put('/cancel/:id', authMiddleware, foodDonationController.cancelFoodDonationController);

export default router;